document.addEventListener('DOMContentLoaded', () => {
  // DOM 元素
  const keysList = document.getElementById('keysList');
  const addBtn = document.getElementById('addBtn');
  const editModal = document.getElementById('editModal');
  const modalTitle = document.getElementById('modalTitle');
  const editForm = document.getElementById('editForm');
  const editId = document.getElementById('editId');
  const keywordInput = document.getElementById('keyword');
  const secretInput = document.getElementById('secret');
  const notesInput = document.getElementById('notes');
  const cancelBtn = document.getElementById('cancelBtn');
  
  // 存储所有密钥数据
  let keysData = [];
  // 存储定时器ID，用于更新验证码
  let timers = {};
  
  // 初始化
  loadKeys();
  
  // 加载所有密钥
  function loadKeys() {
    chrome.storage.sync.get('twoFaKeys', (result) => {
      keysData = result.twoFaKeys || [];
      renderKeysList();
      startTimers();
    });
  }
  
  // 渲染密钥列表
  function renderKeysList() {
    // 清除现有内容
    keysList.innerHTML = '';
    
    // 如果没有密钥，显示空状态
    if (keysData.length === 0) {
      const emptyRow = document.createElement('tr');
      emptyRow.innerHTML = `
        <td colspan="6" class="empty-state">
          <i class="fa fa-key"></i>
          <p>还没有添加任何2FA密钥</p>
          <p>点击"添加新密钥"按钮开始使用</p>
        </td>
      `;
      keysList.appendChild(emptyRow);
      return;
    }
    
    // 渲染每个密钥
    keysData.forEach(key => {
      const row = document.createElement('tr');
      row.dataset.id = key.id;
      
      // 计算当前OTP值
      const otp = calculateOTP(key.secret);
      // 计算剩余时间
      const timeRemaining = 30 - (Math.floor(Date.now() / 1000) % 30);
      
      row.innerHTML = `
        <td>${escapeHtml(key.keyword)}</td>
        <td>${maskSecret(key.secret)}</td>
        <td class="otp-value">${otp}</td>
        <td class="expiry" data-id="${key.id}">${timeRemaining}</td>
        <td>${escapeHtml(key.notes || '')}</td>
        <td class="actions-cell">
          <button class="delete-btn" data-id="${key.id}" title="删除">
            <i class="fa fa-trash"></i>
          </button>
        </td>
      `;

      // 双击行编辑
      row.addEventListener('dblclick', (e) => {
        // 如果点击的是删除按钮，不触发编辑
        if (e.target.closest('.delete-btn')) return;
        editKey(key.id);
      });

      // 删除按钮事件
      const deleteBtn = row.querySelector('.delete-btn');
      deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        deleteKey(key.id);
      });
      
      keysList.appendChild(row);
    });
  }
  
  // 开始定时器，更新验证码和倒计时
  function startTimers() {
    // 清除现有定时器
    Object.values(timers).forEach(timer => clearInterval(timer));
    timers = {};
    
    // 为每个密钥设置定时器
    keysData.forEach(key => {
      timers[key.id] = setInterval(() => {
        const now = Math.floor(Date.now() / 1000);
        const timeRemaining = 30 - (now % 30);
        
        // 更新倒计时显示
        const expiryEl = document.querySelector(`.expiry[data-id="${key.id}"]`);
        if (expiryEl) {
          expiryEl.textContent = timeRemaining;
        }
        
        // 每30秒更新一次OTP值
        if (now % 30 === 0) {
          const otp = calculateOTP(key.secret);
          const row = document.querySelector(`tr[data-id="${key.id}"]`);
          if (row) {
            row.querySelector('.otp-value').textContent = otp;
          }
        }
      }, 1000);
    });
  }
  
  // 计算OTP值
  function calculateOTP(secret) {
    try {
      const totp = new OTPAuth.TOTP({
        secret: OTPAuth.Secret.fromBase32(secret),
        digits: 6,
        period: 30
      });
      return totp.generate();
    } catch (error) {
      console.error('计算OTP失败:', error);
      return '无效密钥';
    }
  }
  
  // 掩码处理密钥，只显示前4位和后4位
  function maskSecret(secret) {
    if (secret.length <= 8) return secret;
    return secret.substring(0, 4) + '...' + secret.substring(secret.length - 4);
  }
  
  // HTML转义，防止XSS
  function escapeHtml(text) {
    if (!text) return '';
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }
  
  // 打开添加密钥模态框
  function openAddModal() {
    modalTitle.textContent = '添加新密钥';
    editId.value = '';
    keywordInput.value = '';
    secretInput.value = '';
    notesInput.value = '';
    editModal.style.display = 'flex';
    keywordInput.focus();
  }
  
  // 打开编辑密钥模态框
  function editKey(id) {
    const key = keysData.find(k => k.id === id);
    if (!key) return;
    
    modalTitle.textContent = '编辑密钥';
    editId.value = id;
    keywordInput.value = key.keyword;
    secretInput.value = key.secret;
    notesInput.value = key.notes || '';
    editModal.style.display = 'flex';
    keywordInput.focus();
  }
  
  // 保存密钥
  function saveKey(e) {
    e.preventDefault();
    
    const id = editId.value || generateId();
    const keyword = keywordInput.value.trim();
    const secret = secretInput.value.trim().replace(/\s/g, ''); // 移除所有空格
    const notes = notesInput.value.trim();
    
    // 验证输入
    if (!keyword || !secret) {
      alert('关键词和密钥为必填项');
      return;
    }
    
    // 检查是否是新密钥
    const index = keysData.findIndex(k => k.id === id);
    
    if (index >= 0) {
      // 更新现有密钥
      keysData[index] = { ...keysData[index], keyword, secret, notes };
    } else {
      // 添加新密钥
      keysData.push({
        id,
        keyword,
        secret,
        notes,
        createdAt: new Date().toISOString()
      });
    }
    
    // 保存到存储
    chrome.storage.sync.set({ twoFaKeys: keysData }, () => {
      // 重新加载和渲染
      loadKeys();
      // 关闭模态框
      editModal.style.display = 'none';
    });
  }
  
  // 删除密钥
  function deleteKey(id) {
    const key = keysData.find(k => k.id === id);
    if (!key) return;

    // 确认删除
    if (confirm(`确定要删除密钥 "${key.keyword}" 吗？此操作不可撤销。`)) {
      // 从数组中移除
      keysData = keysData.filter(k => k.id !== id);

      // 清除对应的定时器
      if (timers[id]) {
        clearInterval(timers[id]);
        delete timers[id];
      }

      // 保存到存储
      chrome.storage.sync.set({ twoFaKeys: keysData }, () => {
        // 重新渲染列表
        renderKeysList();
        startTimers();
      });
    }
  }

  // 生成唯一ID
  function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }
  
  // 事件监听
  addBtn.addEventListener('click', openAddModal);
  editForm.addEventListener('submit', saveKey);
  cancelBtn.addEventListener('click', () => {
    editModal.style.display = 'none';
  });
  
  // 点击模态框外部关闭
  editModal.addEventListener('click', (e) => {
    if (e.target === editModal) {
      editModal.style.display = 'none';
    }
  });
  
  // 清理定时器
  window.addEventListener('unload', () => {
    Object.values(timers).forEach(timer => clearInterval(timer));
  });
});
    