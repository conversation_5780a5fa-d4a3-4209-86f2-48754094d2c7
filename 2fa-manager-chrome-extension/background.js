// Background script for handling messages between content script and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getMatchingOTP') {
    // 获取存储的密钥数据
    chrome.storage.sync.get('twoFaKeys', (result) => {
      const keysData = result.twoFaKeys || [];
      const currentDomain = request.domain;

      // 查找匹配的密钥
      const matchedKey = keysData.find(key =>
        currentDomain.includes(key.keyword.toLowerCase())
      );

      if (matchedKey) {
        // 计算OTP（这里需要实现TOTP算法或使用库）
        const otp = calculateTOTP(matchedKey.secret);
        sendResponse({
          success: true,
          otp: otp,
          keyword: matchedKey.keyword
        });
      } else {
        sendResponse({
          success: false,
          message: '未找到匹配的2FA密钥'
        });
      }
    });
    return true; // 保持消息通道开放
  }
});

// TOTP计算函数（简化版本）
function calculateTOTP(secret) {
  try {
    // 这里应该使用完整的OTPAuth库
    // 为了演示，使用简化的算法
    const now = Math.floor(Date.now() / 1000);
    const timeStep = Math.floor(now / 30);

    // 简化的HMAC-SHA1实现（实际应该使用crypto库）
    const hash = simpleHMAC(secret, timeStep);
    const offset = hash & 0xf;
    const code = ((hash >> offset) & 0x7fffffff) % 1000000;

    return code.toString().padStart(6, '0');
  } catch (error) {
    console.error('计算TOTP失败:', error);
    return '000000';
  }
}

// 简化的HMAC实现（仅用于演示）
function simpleHMAC(key, counter) {
  let hash = 0;
  const data = key + counter.toString();
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash);
}
