// Background script for handling messages between content script and popup
// 导入OTPAuth库
importScripts('libs/otpauth.umd.min.js');

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getMatchingOTP') {
    // 获取存储的密钥数据
    chrome.storage.sync.get('twoFaKeys', (result) => {
      const keysData = result.twoFaKeys || [];
      const currentDomain = request.domain;

      // 查找匹配的密钥
      const matchedKey = keysData.find(key =>
        currentDomain.includes(key.keyword.toLowerCase())
      );

      if (matchedKey) {
        // 使用与popup.js相同的OTP计算方法
        const otp = calculateOTP(matchedKey.secret);
        sendResponse({
          success: true,
          otp: otp,
          keyword: matchedKey.keyword
        });
      } else {
        sendResponse({
          success: false,
          message: '未找到匹配的2FA密钥'
        });
      }
    });
    return true; // 保持消息通道开放
  }
});

// 与popup.js中完全相同的OTP计算函数
function calculateOTP(secret) {
  try {
    const totp = new OTPAuth.TOTP({
      secret: OTPAuth.Secret.fromBase32(secret),
      digits: 6,
      period: 30
    });
    return totp.generate();
  } catch (error) {
    console.error('计算OTP失败:', error);
    return '无效密钥';
  }
}
